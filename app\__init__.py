"""
Profi-Anlauf Issues Management System Flask应用工厂
负责创建和配置Flask应用实例
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import config

# 初始化Flask扩展
db = SQLAlchemy()
migrate = Migrate()


def create_app(config_name=None):
    """
    应用工厂函数
    
    Args:
        config_name (str): 配置环境名称，可选值：development, production, testing
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 确定配置环境
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # 应用环境特定的初始化
    config[config_name].init_app(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册模板上下文处理器
    register_template_context(app)
    
    # 注册Shell上下文处理器
    register_shell_context(app)
    
    # 注册时区工具的模板过滤器
    register_timezone_filters(app)
    
    # 确保数据库字段完整性
    with app.app_context():
        ensure_database_integrity()

        # 启动文件监控服务（仅在非测试环境下）
        if config_name != 'testing':
            try:
                from app.views.knowledge_base import get_file_monitor_service
                monitor_service = get_file_monitor_service()
                if not monitor_service.get_status().get('is_running'):
                    monitor_service.start_monitoring()
                    app.logger.info("文件监控服务已自动启动")
            except Exception as e:
                app.logger.warning(f"自动启动文件监控服务失败: {e}")

    return app


def ensure_database_integrity():
    """确保数据库字段完整性"""
    try:
        from app.models.project_assignment import ProjectAssignment
        ProjectAssignment.ensure_reporters_column()
    except Exception as e:
        print(f"数据库完整性检查失败: {e}")
        pass


def register_blueprints(app):
    """注册应用蓝图"""
    from app.views.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.views.issues import issues as issues_blueprint
    app.register_blueprint(issues_blueprint, url_prefix='/issues')
    
    from app.views.import_data import import_data as import_blueprint
    app.register_blueprint(import_blueprint, url_prefix='/import')
    
    from app.views.user import user as user_blueprint
    app.register_blueprint(user_blueprint, url_prefix='/user')
    
    from app.views.admin import admin as admin_blueprint
    app.register_blueprint(admin_blueprint, url_prefix='/admin')
    
    # 注册API蓝图
    from app.views.api import api as api_blueprint
    app.register_blueprint(api_blueprint)

    # 注册知识库蓝图
    from app.views.knowledge_base import knowledge_base as kb_blueprint
    app.register_blueprint(kb_blueprint)


def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        """404错误处理器"""
        from flask import render_template
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理器"""
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(413)
    def too_large(error):
        """文件过大错误处理器"""
        from flask import render_template, flash
        flash('上传文件过大，请选择小于16MB的文件。', 'error')
        return render_template('errors/413.html'), 413


def register_template_context(app):
    """注册模板上下文处理器"""
    
    @app.context_processor
    def inject_config():
        """向模板注入配置变量"""
        from secrets import token_urlsafe
        from datetime import datetime
        
        def csrf_token():
            """生成CSRF token"""
            # 简单的CSRF token生成（实际项目中应该使用更安全的方式）
            return token_urlsafe(32)
        
        def now():
            """返回当前时间，用于模板中的时间比较"""
            return datetime.now()
        
        return {
            'app_name': 'Profi-Anlauf Issues Management System',
            'app_version': '1.0.0',
            'csrf_token': csrf_token,
            'now': now
        }


def register_shell_context(app):
    """注册Shell上下文处理器"""
    
    @app.shell_context_processor
    def make_shell_context():
        """为flask shell命令提供上下文"""
        from app.models import Issue, User, Project, Comment, Tag
        return {
            'db': db,
            'Issue': Issue,
            'User': User,
            'Project': Project,
            'Comment': Comment,
            'Tag': Tag
        }


def register_timezone_filters(app):
    """注册时区工具的模板过滤器"""
    from app.utils.timezone_utils import register_filters
    register_filters(app) 