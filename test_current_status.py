#!/usr/bin/env python3
"""
测试当前监控服务状态
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_current_status():
    """测试当前监控服务状态"""
    print("=== 测试当前监控服务状态 ===")
    
    try:
        from app import create_app
        from app.views.knowledge_base import get_file_monitor_service, FULL_KB_AVAILABLE
        
        app = create_app()
        
        with app.app_context():
            print(f"1. FULL_KB_AVAILABLE: {FULL_KB_AVAILABLE}")
            
            # 获取监控服务
            monitor_service = get_file_monitor_service()
            print(f"2. 监控服务类型: {type(monitor_service).__name__}")
            print(f"3. 监控服务模块: {monitor_service.__class__.__module__}")
            
            # 获取状态
            status = monitor_service.get_status()
            
            print("\n=== 详细状态信息 ===")
            print(f"运行状态: {'运行中' if status.get('is_running') else '已停止'}")
            print(f"自动索引: {'启用' if status.get('auto_index') else '禁用'}")
            print(f"Watchdog可用: {'是' if status.get('watchdog_available') else '否'}")
            print(f"Watchdog运行: {'是' if status.get('watchdog_running') else '否'}")
            print(f"队列大小: {status.get('queue_size', 0)}")
            print(f"定时扫描: {'启用' if status.get('periodic_scan_enabled') else '禁用'}")
            print(f"监控目录: {status.get('watch_directories', [])}")
            
            # 检查监控目录是否存在
            watch_dirs = status.get('watch_directories', [])
            print(f"\n=== 监控目录检查 ===")
            for i, dir_path in enumerate(watch_dirs):
                exists = os.path.exists(dir_path)
                print(f"{i+1}. {dir_path} - {'存在' if exists else '不存在'}")
            
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_current_status()
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
