#!/usr/bin/env python3
"""
测试简化版监控服务中的watchdog问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_watchdog_in_simple_monitor():
    """测试简化版监控服务中的watchdog"""
    print("=== 测试简化版监控服务中的watchdog ===")
    
    try:
        # 1. 直接测试watchdog导入
        print("1. 直接测试watchdog导入...")
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            print("   ✅ 直接导入成功")
        except ImportError as e:
            print(f"   ❌ 直接导入失败: {e}")
            return False
        
        # 2. 测试简化版模块中的WATCHDOG_AVAILABLE
        print("2. 测试简化版模块中的WATCHDOG_AVAILABLE...")
        try:
            from app.utils.file_monitor_simple import WATCHDOG_AVAILABLE
            print(f"   WATCHDOG_AVAILABLE: {WATCHDOG_AVAILABLE}")
            
            if not WATCHDOG_AVAILABLE:
                print("   ❌ WATCHDOG_AVAILABLE为False，这是问题所在！")
                return False
        except Exception as e:
            print(f"   获取WATCHDOG_AVAILABLE失败: {e}")
            return False
        
        # 3. 测试创建简化版监控服务
        print("3. 测试创建简化版监控服务...")
        try:
            from app import create_app
            app = create_app()
            
            with app.app_context():
                from app.utils.file_monitor_simple import get_simple_file_monitor_service
                monitor_service = get_simple_file_monitor_service()
                
                print(f"   监控服务类型: {type(monitor_service).__name__}")
                
                # 检查服务内部的WATCHDOG_AVAILABLE
                if hasattr(monitor_service, '__class__'):
                    module_name = monitor_service.__class__.__module__
                    print(f"   服务模块: {module_name}")
                
                # 获取状态
                status = monitor_service.get_status()
                print(f"   watchdog_available: {status.get('watchdog_available')}")
                print(f"   is_running: {status.get('is_running')}")
                
                # 如果没有运行，尝试启动
                if not status.get('is_running'):
                    print("4. 尝试启动监控服务...")
                    try:
                        monitor_service.start_monitoring()
                        import time
                        time.sleep(1)
                        
                        new_status = monitor_service.get_status()
                        print(f"   启动后watchdog_available: {new_status.get('watchdog_available')}")
                        print(f"   启动后is_running: {new_status.get('is_running')}")
                        
                    except Exception as e:
                        print(f"   启动失败: {e}")
                        import traceback
                        traceback.print_exc()
                
        except Exception as e:
            print(f"   创建监控服务失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_watchdog_in_simple_monitor()
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
