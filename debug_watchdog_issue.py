#!/usr/bin/env python3
"""
调试watchdog问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_watchdog_issue():
    """调试watchdog问题"""
    print("=== 调试watchdog问题 ===")
    
    try:
        # 1. 测试直接导入
        print("1. 测试直接导入watchdog...")
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            print("   ✅ 直接导入成功")
        except ImportError as e:
            print(f"   ❌ 直接导入失败: {e}")
            return False
        
        # 2. 测试模块级别的WATCHDOG_AVAILABLE
        print("2. 测试模块级别的WATCHDOG_AVAILABLE...")
        try:
            from app.utils.file_monitor_simple import WATCHDOG_AVAILABLE
            print(f"   WATCHDOG_AVAILABLE: {WATCHDOG_AVAILABLE}")
        except Exception as e:
            print(f"   获取失败: {e}")
            return False
        
        # 3. 测试应用上下文中的导入
        print("3. 测试应用上下文中的导入...")
        try:
            from app import create_app
            app = create_app()
            
            with app.app_context():
                print("   应用上下文创建成功")
                
                # 重新测试导入
                try:
                    from watchdog.observers import Observer
                    from watchdog.events import FileSystemEventHandler
                    print("   ✅ 应用上下文中导入成功")
                except ImportError as e:
                    print(f"   ❌ 应用上下文中导入失败: {e}")
                    return False
                
                # 测试创建Observer
                try:
                    observer = Observer()
                    print("   ✅ Observer创建成功")
                    observer.stop()  # 立即停止
                except Exception as e:
                    print(f"   ❌ Observer创建失败: {e}")
                    return False
                
        except Exception as e:
            print(f"   应用上下文创建失败: {e}")
            return False
        
        # 4. 测试监控服务
        print("4. 测试监控服务...")
        try:
            from app.views.knowledge_base import get_file_monitor_service
            
            with app.app_context():
                monitor_service = get_file_monitor_service()
                print(f"   监控服务类型: {type(monitor_service).__name__}")
                
                # 获取状态
                status = monitor_service.get_status()
                print(f"   watchdog_available: {status.get('watchdog_available')}")
                print(f"   is_running: {status.get('is_running')}")
                
                # 如果是简化版，检查WATCHDOG_AVAILABLE
                if hasattr(monitor_service, '__class__') and 'simple' in monitor_service.__class__.__module__:
                    from app.utils.file_monitor_simple import WATCHDOG_AVAILABLE as SIMPLE_WATCHDOG
                    print(f"   简化版WATCHDOG_AVAILABLE: {SIMPLE_WATCHDOG}")
                
        except Exception as e:
            print(f"   监控服务测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = debug_watchdog_issue()
    if success:
        print("\n✅ 调试完成")
    else:
        print("\n❌ 调试失败")
